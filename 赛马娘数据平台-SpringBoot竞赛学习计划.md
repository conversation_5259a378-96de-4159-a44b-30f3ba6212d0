# 赛马娘数据平台 - SpringBoot竞赛学习计划

## 🎯 项目定位与双重目标

### 核心理念：一个项目，双重收益
通过开发**赛马娘数据分析平台**，同时完成：
- ✅ **实际项目**：功能完整的赛马娘辅助工具
- ✅ **竞赛准备**：掌握应用软件系统开发赛项所有技能点

### 技术栈完美匹配
| 项目需求 | 技术选择 | 竞赛要求 | 匹配度 |
|---------|---------|---------|-------|
| 前端界面 | Vue.js + ElementUI | Vue.js + ElementUI | ✅ 100% |
| 后端服务 | Spring Boot + MySQL | Spring Boot + MySQL | ✅ 100% |
| 数据可视化 | ECharts | ECharts图表 | ✅ 100% |
| 项目管理 | Maven | Maven打包 | ✅ 100% |
| 部署方案 | Nginx多端口 | Nginx部署 | ✅ 100% |

---

## 📋 竞赛任务与项目功能对应

### 竞赛任务分析
1. **任务1：承运申请审核**（10分）→ **项目对应**：成员管理审核
2. **任务2：销售发货管理**（12分）→ **项目对应**：数据管理CRUD
3. **任务3：政策法规**（11分）→ **项目对应**：用户端界面
4. **任务4：参与投标**（12分）→ **项目对应**：数据整合展示
5. **任务5：数据可视化**（10分）→ **项目对应**：图表分析

### 项目功能设计（对标竞赛）
```typescript
// 赛马娘平台功能模块
interface UmamusumeModules {
  // 对应任务1: 管理端界面
  adminPanel: {
    memberAudit: '成员审核管理',      // 练习表单验证、状态更新
    dataReview: '数据审核流程',       // 练习工作流程
    systemConfig: '系统配置'          // 练习配置管理
  },
  
  // 对应任务2: 全栈CRUD
  dataManagement: {
    fanDataCRUD: '粉丝数据管理',      // 完整CRUD操作
    memberCRUD: '成员信息管理',       // 分页、搜索、排序
    batchImport: '批量数据导入'       // 文件上传处理
  },
  
  // 对应任务3: 用户界面
  userPortal: {
    dashboard: '用户仪表板',          // 用户端界面设计
    dataQuery: '数据查询',           // 查询表单设计
    reportExport: '报表导出'          // 数据导出功能
  },
  
  // 对应任务4: 数据整合
  analytics: {
    dataIntegration: '数据整合分析',   // 多表联查
    trendAnalysis: '趋势分析',        // 复杂统计
    comparison: '对比分析'            // 数据对比
  },
  
  // 对应任务5: 数据可视化
  visualization: {
    gaugeCharts: '仪表盘图表',        // 仪表盘、环状图
    barCharts: '柱状图统计',          // 柱形图、曲线图
    scrollTable: '滚动表格',          // 滚动数据表格
    realTimeUpdate: '实时数据更新'     // 动态数据展示
  }
}
```

---

## 📅 四周学习计划 (结合竞赛准备)

### 第一周：SpringBoot基础 + 项目搭建

#### Day 1-2: SpringBoot环境搭建
**学习目标**：掌握SpringBoot基础架构
```bash
# 竞赛技能点
- [ ] Spring Boot项目创建和配置
- [ ] Controller、Service、DAO分层架构理解
- [ ] Maven项目管理和依赖配置
- [ ] application.yml配置文件使用

# 项目实践
- [ ] 创建赛马娘数据平台后端项目
- [ ] 配置MySQL数据库连接
- [ ] 实现第一个Hello World接口
- [ ] 练习Maven打包生成jar文件
```

**代码示例**：
```java
// UmamusumeDataController.java - 第一个接口
@RestController
@RequestMapping("/api/data")
public class UmamusumeDataController {
    
    @GetMapping("/hello")
    public ResponseEntity<String> hello() {
        return ResponseEntity.ok("赛马娘数据平台启动成功！");
    }
}
```

#### Day 3-4: 数据库设计
**学习目标**：设计完整的业务数据库
```sql
-- 竞赛技能点：表结构设计
CREATE TABLE fan_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_name VARCHAR(100) NOT NULL COMMENT '成员名称',
    fan_count BIGINT NOT NULL COMMENT '粉丝数量',
    record_date DATE NOT NULL COMMENT '记录日期',
    society_name VARCHAR(100) COMMENT '社团名称',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_member_date (member_name, record_date),
    INDEX idx_record_date (record_date),
    INDEX idx_member_name (member_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='粉丝数据表';

-- 对应竞赛任务1：审核表
CREATE TABLE audit_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    data_id BIGINT NOT NULL,
    audit_type VARCHAR(50) NOT NULL,
    audit_status VARCHAR(20) NOT NULL,
    auditor VARCHAR(100),
    audit_time TIMESTAMP,
    remarks TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Day 5-7: RESTful API开发
**学习目标**：实现完整的CRUD接口
```java
// 对应竞赛任务2：销售发货管理 → 数据管理CRUD
@RestController
@RequestMapping("/api/fandata")
public class FanDataController {
    
    @Autowired
    private FanDataService fanDataService;
    
    // GET - 分页查询 (竞赛重点)
    @GetMapping("/list")
    public ResponseEntity<PageResult<FanData>> list(
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "10") Integer size,
        @RequestParam(required = false) String memberName,
        @RequestParam(required = false) String startDate,
        @RequestParam(required = false) String endDate) {
        
        PageResult<FanData> result = fanDataService.queryByPage(
            page, size, memberName, startDate, endDate);
        return ResponseEntity.ok(result);
    }
    
    // POST - 新增数据
    @PostMapping("/add")
    public ResponseEntity<String> add(@RequestBody FanData fanData) {
        fanDataService.save(fanData);
        return ResponseEntity.ok("数据添加成功");
    }
    
    // PUT - 更新数据
    @PutMapping("/update")
    public ResponseEntity<String> update(@RequestBody FanData fanData) {
        fanDataService.update(fanData);
        return ResponseEntity.ok("数据更新成功");
    }
    
    // DELETE - 删除数据
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> delete(@PathVariable Long id) {
        fanDataService.deleteById(id);
        return ResponseEntity.ok("数据删除成功");
    }
}
```

### 第二周：Vue前端开发 + ElementUI

#### Day 8-9: Vue.js基础复习
**学习目标**：掌握Vue.js和ElementUI组件
```vue
<!-- 对应竞赛任务1：承运申请审核 → 成员审核管理 -->
<template>
  <div class="audit-management">
    <!-- ElementUI表格 - 竞赛重点组件 -->
    <el-table 
      :data="auditList" 
      border 
      style="width: 100%">
      <el-table-column prop="memberName" label="成员名称" width="150" />
      <el-table-column prop="fanCount" label="粉丝数量" width="120" />
      <el-table-column prop="recordDate" label="记录日期" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'warning'">
            {{ scope.row.status === 1 ? '已审核' : '待审核' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            @click="handleAudit(scope.$index, scope.row)">
            审核
          </el-button>
          <el-button 
            size="mini" 
            type="danger" 
            @click="handleReject(scope.$index, scope.row)">
            拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 - 竞赛重点 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      auditList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    // 竞赛重点：分页查询
    loadData() {
      this.$http.get('/api/audit/list', {
        params: {
          page: this.currentPage,
          size: this.pageSize
        }
      }).then(response => {
        this.auditList = response.data.records;
        this.total = response.data.total;
      });
    }
  }
}
</script>
```

#### Day 10-11: 表单和对话框开发
**学习目标**：掌握表单验证和对话框操作
```vue
<!-- 对应竞赛任务2：销售发货管理 → 数据管理表单 -->
<template>
  <div>
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="成员名称">
        <el-input v-model="searchForm.memberName" placeholder="请输入成员名称" />
      </el-form-item>
      <el-form-item label="记录日期">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="success" @click="showAddDialog">新增</el-button>
      </el-form-item>
    </el-form>
    
    <!-- 新增/编辑对话框 - 竞赛重点 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px">
      <el-form 
        :model="dataForm" 
        :rules="dataRules" 
        ref="dataForm" 
        label-width="100px">
        <el-form-item label="成员名称" prop="memberName">
          <el-input v-model="dataForm.memberName" />
        </el-form-item>
        <el-form-item label="粉丝数量" prop="fanCount">
          <el-input-number 
            v-model="dataForm.fanCount" 
            :min="0" 
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="记录日期" prop="recordDate">
          <el-date-picker
            v-model="dataForm.recordDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
```

### 第三周：数据可视化 + 前后端联调

#### Day 12-13: ECharts图表开发
**学习目标**：实现竞赛要求的所有图表类型
```vue
<!-- 对应竞赛任务5：数据可视化 -->
<template>
  <div class="visualization-dashboard">
    <!-- 仪表盘图表 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card title="粉丝增长率">
          <div id="gaugeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="活跃度占比">
          <div id="pieChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="数据统计">
          <div id="barChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 滚动表格 - 竞赛重点 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card title="实时数据监控">
          <div class="scroll-table">
            <el-table 
              :data="scrollData" 
              height="400" 
              border>
              <el-table-column prop="memberName" label="成员名称" width="150" />
              <el-table-column prop="currentFans" label="当前粉丝" width="120" />
              <el-table-column prop="growth" label="增长量" width="120" />
              <el-table-column prop="growthRate" label="增长率" width="120" />
              <el-table-column prop="updateTime" label="更新时间" />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  data() {
    return {
      scrollData: [],
      charts: {}
    }
  },
  mounted() {
    this.initCharts();
    this.loadData();
    // 竞赛要求：实时数据更新
    this.timer = setInterval(() => {
      this.updateData();
    }, 5000);
  },
  methods: {
    // 初始化所有图表
    initCharts() {
      // 仪表盘图表
      this.charts.gauge = echarts.init(document.getElementById('gaugeChart'));
      this.charts.gauge.setOption({
        series: [{
          type: 'gauge',
          detail: { fontSize: 30 },
          data: [{ value: 85, name: '增长率%' }]
        }]
      });
      
      // 环状图
      this.charts.pie = echarts.init(document.getElementById('pieChart'));
      this.charts.pie.setOption({
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 35, name: '高活跃' },
            { value: 45, name: '中活跃' },
            { value: 20, name: '低活跃' }
          ]
        }]
      });
      
      // 柱状图
      this.charts.bar = echarts.init(document.getElementById('barChart'));
      this.charts.bar.setOption({
        xAxis: { type: 'category', data: ['涯migiwa', 'Svengalis', 'ネオス'] },
        yAxis: { type: 'value' },
        series: [{
          type: 'bar',
          data: [3559, 5747, 6398]
        }]
      });
    }
  }
}
</script>
```

#### Day 14-15: 前后端联调
**学习目标**：完成完整的数据流程
```java
// 后端：数据可视化接口
@RestController
@RequestMapping("/api/visualization")
public class VisualizationController {
    
    // 竞赛任务5：获取仪表盘数据
    @GetMapping("/dashboard")
    public ResponseEntity<DashboardData> getDashboardData() {
        DashboardData data = new DashboardData();
        
        // 仪表盘数据
        data.setGrowthRate(calculateGrowthRate());
        
        // 环状图数据
        data.setPieData(calculateActivityData());
        
        // 柱状图数据
        data.setBarData(getTopMembersData());
        
        // 滚动表格数据
        data.setScrollData(getRealTimeData());
        
        return ResponseEntity.ok(data);
    }
    
    // 实时数据推送
    @GetMapping("/realtime")
    public ResponseEntity<List<ScrollTableData>> getRealTimeData() {
        List<ScrollTableData> data = fanDataService.getRealTimeScrollData();
        return ResponseEntity.ok(data);
    }
}
```

### 第四周：项目部署 + 竞赛模拟

#### Day 16-17: 项目打包部署
**学习目标**：掌握Maven打包和Nginx部署
```bash
# 竞赛要求：Maven打包
cd backend
mvn clean package -DskipTests

# 前端打包
cd frontend
npm run build

# Nginx配置 - 竞赛重点：多端口部署
# /etc/nginx/sites-available/umamusume
server {
    listen 8088;  # 管理端
    server_name localhost;
    root /var/www/umamusume/admin;
    index index.html;
    
    location /api/ {
        proxy_pass http://localhost:8080/;
    }
}

server {
    listen 8081;  # 用户端
    server_name localhost;
    root /var/www/umamusume/user;
    index index.html;
}

server {
    listen 8080;  # 可视化端
    server_name localhost;
    root /var/www/umamusume/visualization;
    index index.html;
}
```

#### Day 18-20: 竞赛模拟练习
**学习目标**：4小时完成所有任务的时间训练

**模拟竞赛时间分配**：
```bash
# 任务1：成员审核管理 (10分) - 45分钟
- [ ] 实现审核列表页面 (ElementUI表格)
- [ ] 实现审核状态更新功能
- [ ] 添加搜索和分页

# 任务2：数据管理CRUD (12分) - 60分钟  
- [ ] 完整的增删改查接口
- [ ] 前端表单和表格页面
- [ ] 数据验证和错误处理

# 任务3：用户端界面 (11分) - 50分钟
- [ ] 用户端数据查询页面
- [ ] 数据展示和筛选功能
- [ ] 响应式布局

# 任务4：数据整合分析 (12分) - 60分钟
- [ ] 多表联查接口开发
- [ ] 数据对比分析页面
- [ ] 统计数据展示

# 任务5：数据可视化 (10分) - 45分钟
- [ ] 仪表盘、环状图、柱状图
- [ ] 滚动表格实现
- [ ] 实时数据更新
```

---

## 🏗️ 项目架构设计 (SpringBoot版)

### 后端架构
```
uma-platform-backend/
├── src/main/java/com/umamusume/
│   ├── UmaPlatformApplication.java          # 启动类
│   ├── controller/                          # 控制器层
│   │   ├── FanDataController.java          # 粉丝数据接口
│   │   ├── AuditController.java            # 审核管理接口
│   │   ├── VisualizationController.java    # 可视化接口
│   │   └── UserController.java             # 用户管理接口
│   ├── service/                            # 业务层
│   │   ├── FanDataService.java
│   │   ├── AuditService.java
│   │   └── VisualizationService.java
│   ├── dao/                                # 数据访问层
│   │   ├── FanDataMapper.java
│   │   └── AuditMapper.java
│   ├── entity/                             # 实体类
│   │   ├── FanData.java
│   │   └── AuditRecord.java
│   ├── dto/                                # 数据传输对象
│   └── config/                             # 配置类
├── src/main/resources/
│   ├── application.yml                     # 配置文件
│   ├── mapper/                            # MyBatis映射文件
│   └── static/                            # 静态资源
└── pom.xml                                # Maven配置
```

### 前端架构
```
uma-platform-frontend/
├── admin/                                  # 管理端 (端口8088)
│   ├── src/
│   │   ├── views/
│   │   │   ├── audit/                     # 审核管理
│   │   │   ├── data/                      # 数据管理
│   │   │   └── system/                    # 系统管理
│   │   └── components/
├── user/                                   # 用户端 (端口8081)
│   ├── src/
│   │   ├── views/
│   │   │   ├── dashboard/                 # 用户仪表板
│   │   │   └── query/                     # 数据查询
│   │   └── components/
└── visualization/                          # 可视化端 (端口8080)
    ├── src/
    │   ├── views/
    │   │   ├── charts/                    # 图表页面
    │   │   └── monitor/                   # 实时监控
    │   └── components/
```

---

## 📊 学习进度追踪

### 第一周完成情况
- [ ] **Day 1-2**: SpringBoot环境搭建和基础配置
- [ ] **Day 3-4**: MySQL数据库设计和连接
- [ ] **Day 5-7**: RESTful API接口开发

### 第二周完成情况
- [ ] **Day 8-9**: Vue.js基础和ElementUI组件
- [ ] **Day 10-11**: 表单验证和对话框开发

### 第三周完成情况
- [ ] **Day 12-13**: ECharts图表开发
- [ ] **Day 14-15**: 前后端联调测试

### 第四周完成情况
- [ ] **Day 16-17**: 项目打包和部署配置
- [ ] **Day 18-20**: 竞赛模拟和时间训练

### 竞赛技能检查清单
- [ ] **Spring Boot**：分层架构、注解使用、配置管理
- [ ] **Vue.js**：组件开发、数据绑定、事件处理
- [ ] **ElementUI**：表格、表单、对话框、分页组件
- [ ] **MySQL**：表设计、复杂查询、索引优化
- [ ] **ECharts**：仪表盘、环状图、柱状图、滚动表格
- [ ] **Maven**：项目管理、依赖配置、打包部署
- [ ] **Nginx**：多端口配置、反向代理
- [ ] **前后端分离**：API设计、数据交互、错误处理

---

## 🎯 预期学习成果

### 技术收益
- ✅ **SpringBoot开发**：完整掌握企业级后端开发
- ✅ **Vue前端技能**：现代化前端开发能力
- ✅ **全栈思维**：前后端分离架构理解
- ✅ **竞赛技能**：100%覆盖竞赛技术要求

### 项目收益
- ✅ **实用工具**：真正能用的赛马娘数据平台
- ✅ **作品展示**：简历项目和Github展示
- ✅ **技术深度**：不是简单的Demo，而是完整项目

### 竞赛收益
- ✅ **技能完备**：所有竞赛技术点100%覆盖
- ✅ **实战经验**：真实项目开发经验加持
- ✅ **时间把控**：通过项目训练时间管理能力
- ✅ **问题解决**：实际开发中的问题处理能力

---

## 🚀 立即开始行动

### 第一步：环境搭建
```bash
# 1. JDK安装 (JDK 8+)
java -version

# 2. MySQL安装和配置
mysql -u root -p

# 3. Maven安装
mvn -version

# 4. IDE安装 (IntelliJ IDEA 推荐)

# 5. 创建SpringBoot项目
# 访问 https://start.spring.io/
# 选择：Maven + Java + Spring Boot 2.7.x
# 依赖：Spring Web, MyBatis, MySQL Driver
```

### 第二步：项目初始化
```bash
# 创建项目目录
mkdir umamusume-platform
cd umamusume-platform

# 创建后端项目 (通过Spring Initializr)
# 创建前端项目
vue create frontend

# 数据库初始化
mysql -u root -p < database/init.sql
```

### 第三步：第一个功能
**目标**：实现第一个API接口和前端页面
- 后端：实现粉丝数据查询接口
- 前端：实现数据展示页面
- 验证：前后端成功通信

**你准备好开始这个充满挑战和收获的学习之旅了吗？** 🎯

这个计划不仅能让你完成一个有意义的项目，还能为竞赛做好充分准备。有任何技术问题都可以随时问我！