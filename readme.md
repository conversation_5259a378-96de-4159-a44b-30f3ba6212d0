# 社团成员粉丝数据统计工具

## 项目简介

这是一个用于统计和分析社团成员粉丝数变化的工具，支持数据可视化展示。

## 数据格式

JSON 文件存储每日粉丝数据，存放在 `database` 文件夹中，文件名为日期格式（如：20250723.json）

```json
{
    "社团名称":{
        "社团成员id": 粉丝数,
        "社团成员id": 粉丝数,
        "社团成员id": 粉丝数
    }
}
```

## 功能特性

### Python 版本 (read_fans.py)

1. **交互式日期选择**：用户可以选择两个日期进行对比分析
2. **粉丝变化统计**：计算并展示成员间粉丝数变化，单位为万
3. **成员状态跟踪**：
   - 新加入成员：显示增长0粉丝 + 注释（新加入）
   - 退出成员：显示增长0粉丝 + 注释（已退社团）
4. **数据可视化**：
   - **统计面板**：显示有效粉丝增长总数和统计信息
   - **折线图**：展示成员粉丝增长数量变化趋势（单位：万）
   - **饼图**：展示粉丝增长贡献占比（智能处理新成员）
5. **离线支持**：使用本地 echarts.js 文件，无需网络连接

## 环境要求

### Python 版本
- Python 3.6+
- 依赖包：
  ```bash
  pip install tabulate
  ```

## 使用方法

### 运行 Python 程序

```bash
python read_fans.py
```

### 操作步骤

1. 程序启动后会显示所有可用的日期文件
2. 选择第一个日期（输入序号）
3. 选择第二个日期（输入序号）
4. 程序会：
   - 在控制台显示粉丝变化表格
   - 自动生成并打开 `pie_chart.html` 文件
   - 显示饼图和折线图

### 输出文件

- `pie_chart.html`：包含饼图和折线图的可视化页面

## 文件结构

```
├── read_fans.py          # Python 主程序
├── echarts.js           # ECharts 图表库
├── pie_chart.html       # 生成的可视化页面
├── readme.md           # 说明文档
└── database/            # 数据文件夹
    ├── 20250701.json    # 粉丝数据文件
    ├── 20250704.json    # 粉丝数据文件
    └── ...              # 其他日期数据文件
```

## 图表说明

### 统计面板
- 显示统计期间和有效成员数
- **有效粉丝增长总数**：所有截至结束日期仍在社团的成员的粉丝增长总和
  - 老成员：从开始日期到结束日期的增长量
  - 新成员：从加入日期到结束日期的增长量
- 支持正负增长显示，颜色区分

### 饼图
- 显示粉丝增长量占比
- **按增长量排序**：从大到小显示，便于识别主要贡献者
- 智能处理新成员：从加入日期开始计算增长量
- 自动排除退出成员
- 只显示正增长的成员
- 鼠标悬停显示详细信息（增长量、占比）

### 折线图
- 显示成员粉丝增长数量变化趋势
- X轴：日期
- Y轴：粉丝增长量（万为单位）
- 新成员：从加入日期开始统计，加入日期为基准点（增长量为0）
- 退出成员：不显示在图表中
- 支持图例筛选和缩放

## 配置说明

### 数据文件路径配置
程序中的数据文件路径可以通过修改源码中的 `DATA_FOLDER` 变量来调整：

```python
# 配置：数据文件路径
DATA_FOLDER = "database"  # 可以修改此路径来改变数据文件位置
```

## 注意事项

1. 确保 JSON 数据文件格式正确并存放在 `database` 文件夹中
2. 至少需要 2 个数据文件才能进行对比分析
3. 确保 `echarts.js` 文件存在于同一目录下
4. 生成的 HTML 文件可以在任何现代浏览器中打开
5. 新成员会从加入日期开始统计增长量
6. 中途退出的成员不会显示在图表中

## 更新日志

### v2.1 (最新版本)
- ✅ 新增有效粉丝增长总数统计功能
- ✅ 在网页中添加统计面板，显示：
  - 统计期间和有效成员数
  - 有效粉丝增长总数（包含老成员和新成员的智能计算）
- ✅ 网页样式优化：
  - 黑色主题背景设计
  - 去除emoji，采用简洁文字
  - 保持绿色/红色的数值颜色区分
- ✅ 图表黑暗模式：
  - 饼图和折线图采用ECharts黑暗主题
  - 图表背景、文字、坐标轴全部适配黑色主题
- ✅ 饼图显示优化：按粉丝增长量从大到小排序
- ✅ 修复成员名字不一致问题（Beautiful 系列）
- ✅ 完善数据计算逻辑，确保准确性

### v2.0
- ✅ 重构数据文件组织：所有数据文件移至 `database` 文件夹
- ✅ 添加可配置的数据文件路径
- ✅ 优化折线图逻辑：
  - 新成员从加入日期开始统计增长量
  - 退出成员不显示在图表中
  - Y轴显示粉丝增长数量而非总数
- ✅ 优化饼图逻辑：
  - 智能处理新成员，从加入日期计算增长量
  - 自动排除退出成员
- ✅ 改进数据处理的健壮性

### v1.0
- ✅ 基础功能实现
- ✅ 支持粉丝数据对比分析
- ✅ 饼图和折线图可视化
- ✅ 离线 ECharts 支持

## 开发计划

- [ ] C# 版本实现（.NET 8.0 平台）
- [ ] 更多图表类型支持
- [ ] 数据导出功能
- [ ] 批量数据处理功能