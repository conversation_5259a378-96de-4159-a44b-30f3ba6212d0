import os
import json
import webbrowser
from tabulate import tabulate

# 配置：数据文件路径
DATA_FOLDER = "database"  # 可以修改此路径来改变数据文件位置

def load_fans_data(file_path):
    """加载JSON粉丝数据文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def select_dates(files):
    """用户选择两个日期"""
    print("可用的日期文件:")
    for i, file in enumerate(files, 1):
        print(f"{i}. {file}")
    
    idx1 = int(input("请选择第一个日期（输入序号）: ")) - 1
    idx2 = int(input("请选择第二个日期（输入序号）: ")) - 1
    
    return files[idx1], files[idx2]

def calculate_changes(file1, file2):
    """计算两个日期间的粉丝变化"""
    data1 = load_fans_data(file1)
    data2 = load_fans_data(file2)

    # 合并所有社团的成员数据，处理异常数据结构
    members1 = {}
    members2 = {}

    # 处理第一个文件的数据
    for society_name, society_data in data1.items():
        if isinstance(society_data, dict):
            members1.update(society_data)
        else:
            # 异常情况：society_name 是成员名，society_data 是粉丝数
            members1[society_name] = society_data

    # 处理第二个文件的数据
    for society_name, society_data in data2.items():
        if isinstance(society_data, dict):
            members2.update(society_data)
        else:
            # 异常情况：society_name 是成员名，society_data 是粉丝数
            members2[society_name] = society_data

    all_members = sorted(set(members1.keys()) | set(members2.keys()))
    results = []

    for member in all_members:
        fans1 = members1.get(member, 0)
        fans2 = members2.get(member, 0)

        # 转换为万单位
        fans1_wan = round(fans1 / 10000, 2)
        fans2_wan = round(fans2 / 10000, 2)
        change = round((fans2 - fans1) / 10000, 2)

        note = ""
        if member not in members1:
            note = "新加入"
            change = 0
        elif member not in members2:
            note = "已退社团"
            change = 0

        results.append([member, fans1_wan, fans2_wan, change, note])

    return results

def get_json_files():
    """获取数据文件夹中的所有JSON文件"""
    if not os.path.exists(DATA_FOLDER):
        print(f"错误: 数据文件夹 '{DATA_FOLDER}' 不存在")
        return []
    return sorted([f for f in os.listdir(DATA_FOLDER) if f.endswith('.json')])

def get_all_timeline_data():
    """获取所有日期的时间线数据 - 显示粉丝增长数量变化"""
    files = get_json_files()
    raw_timeline_data = {}
    dates = []

    # 首先收集所有原始数据
    for file in files:
        date = file.replace('.json', '')
        dates.append(date)

        try:
            file_path = os.path.join(DATA_FOLDER, file)
            data = load_fans_data(file_path)

            # 检查数据结构并处理异常情况
            for society_name, society_data in data.items():
                # 如果 society_data 是字典（正常情况）
                if isinstance(society_data, dict):
                    for member, fans in society_data.items():
                        if member not in raw_timeline_data:
                            raw_timeline_data[member] = {}
                        raw_timeline_data[member][date] = fans
                else:
                    # 如果 society_data 是整数（异常情况，说明数据结构有问题）
                    # 这种情况下，society_name 实际上是成员名，society_data 是粉丝数
                    member = society_name
                    fans = society_data
                    if member not in raw_timeline_data:
                        raw_timeline_data[member] = {}
                    raw_timeline_data[member][date] = fans

        except Exception as e:
            print(f"警告: 处理文件 {file} 时出错: {e}")
            continue

    # 计算每个成员的粉丝增长数量
    timeline_data = {}

    for member, member_data in raw_timeline_data.items():
        # 找到成员第一次出现的日期和最后一次出现的日期
        member_dates = [date for date in dates if date in member_data]

        if not member_dates:
            continue

        # 如果成员在中途退出（不在最后一个日期），则不显示
        if dates[-1] not in member_dates:
            continue

        timeline_data[member] = {}

        # 找到成员加入的日期（第一次出现的日期）
        join_date = member_dates[0]
        join_fans = member_data[join_date]

        for date in dates:
            if date in member_data:
                current_fans = member_data[date]
                # 计算相对于加入日期的增长量（单位：万）
                if date >= join_date:
                    growth = (current_fans - join_fans) / 10000
                    timeline_data[member][date] = round(growth, 2)
                else:
                    timeline_data[member][date] = None
            else:
                timeline_data[member][date] = None

    return timeline_data, dates

def main():
    # 获取所有JSON文件并按日期排序
    files = get_json_files()
    if len(files) < 2:
        print("需要至少2个数据文件")
        return

    date1, date2 = select_dates(files)
    # 为calculate_changes函数添加完整路径
    file1_path = os.path.join(DATA_FOLDER, date1)
    file2_path = os.path.join(DATA_FOLDER, date2)
    results = calculate_changes(file1_path, file2_path)
    
    # 按粉丝变化量排序（降序）
    results.sort(key=lambda x: x[3], reverse=True)
    
    # 输出表格
    headers = ["成员ID", f"{date1}粉丝数(万)", f"{date2}粉丝数(万)", "变化(万)", "备注"]
    print(tabulate(results, headers=headers, tablefmt="grid"))
    
    # 获取时间线数据
    timeline_data, dates = get_all_timeline_data()
    
    # 计算有效粉丝增长总数
    effective_growth_total = 0
    effective_growth_details = []

    # 生成饼图数据 - 处理新成员和退出成员
    pie_data = []
    total_change = 0

    # 获取所有文件来计算新成员的增长
    files = get_json_files()

    # 加载第一个和第二个日期的数据
    file1_path = os.path.join(DATA_FOLDER, date1)
    file2_path = os.path.join(DATA_FOLDER, date2)
    data1 = load_fans_data(file1_path)
    data2 = load_fans_data(file2_path)

    # 处理数据结构
    members1 = {}
    members2 = {}

    for society_name, society_data in data1.items():
        if isinstance(society_data, dict):
            members1.update(society_data)
        else:
            members1[society_name] = society_data

    for society_name, society_data in data2.items():
        if isinstance(society_data, dict):
            members2.update(society_data)
        else:
            members2[society_name] = society_data

    # 计算每个成员的增长量和有效增长总数
    member_changes = {}

    for member in members2:  # 只考虑在第二个日期存在的成员
        if member in members1:
            # 老成员：计算从第一个日期到第二个日期的增长
            change_fans = members2[member] - members1[member]
            change_wan = change_fans / 10000

            # 添加到有效增长总数（包括负增长）
            effective_growth_total += change_fans
            effective_growth_details.append({
                'member': member,
                'type': '老成员',
                'start_date': date1.replace('.json', ''),
                'start_fans': members1[member],
                'end_fans': members2[member],
                'growth_fans': change_fans,
                'growth_wan': round(change_wan, 2)
            })

            if change_wan > 0:
                member_changes[member] = change_wan
        else:
            # 新成员：找到加入日期，计算从加入日期到第二个日期的增长
            join_date = None
            join_fans = 0

            # 按日期顺序查找成员第一次出现的日期
            for file in files:
                date = file.replace('.json', '')
                if date <= date2.replace('.json', ''):
                    try:
                        file_path = os.path.join(DATA_FOLDER, file)
                        data = load_fans_data(file_path)

                        # 检查成员是否在这个日期存在
                        member_found = False
                        for society_name, society_data in data.items():
                            if isinstance(society_data, dict):
                                if member in society_data:
                                    if join_date is None:
                                        join_date = date
                                        join_fans = society_data[member]
                                    member_found = True
                                    break
                            else:
                                if society_name == member:
                                    if join_date is None:
                                        join_date = date
                                        join_fans = society_data
                                    member_found = True
                                    break

                        if member_found and join_date:
                            break

                    except Exception:
                        continue

            if join_date and join_fans > 0:
                change_fans = members2[member] - join_fans
                change_wan = change_fans / 10000

                # 添加到有效增长总数（包括负增长）
                effective_growth_total += change_fans
                effective_growth_details.append({
                    'member': member,
                    'type': '新成员',
                    'start_date': join_date,
                    'start_fans': join_fans,
                    'end_fans': members2[member],
                    'growth_fans': change_fans,
                    'growth_wan': round(change_wan, 2)
                })

                if change_wan > 0:
                    member_changes[member] = change_wan

    # 计算总增长量
    total_change = sum(member_changes.values())

    # 生成饼图数据并按增长量排序
    for member, change in member_changes.items():
        change_percent = (change / total_change) * 100 if total_change > 0 else 0
        pie_data.append({
            'name': f"{member} (+{round(change, 2)}万)",
            'value': round(change, 2),
            'percent': round(change_percent, 1)
        })

    # 按增长量从大到小排序
    pie_data.sort(key=lambda x: x['value'], reverse=True)
    
    if not pie_data:
        print("没有正增长数据生成饼图")
        return
    
    # 生成折线图数据
    line_series = []
    for member, member_data in timeline_data.items():
        series_data = []
        for date in dates:
            value = member_data.get(date, None)
            series_data.append(value)
        
        line_series.append({
            'name': member,
            'type': 'line',
            'data': series_data,
            'connectNulls': False
        })
    
    # 转换为JSON字符串
    pie_data_json = json.dumps(pie_data, ensure_ascii=False)
    legend_data = [d['name'] for d in pie_data]
    legend_data_json = json.dumps(legend_data, ensure_ascii=False)
    line_series_json = json.dumps(line_series, ensure_ascii=False)
    dates_json = json.dumps(dates, ensure_ascii=False)
    
    # 计算有效增长总数的万单位
    effective_growth_wan = round(effective_growth_total / 10000, 2)

    html_content = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>粉丝数据统计</title>
        <script src="echarts.js"></script>
        <style>
            body {{
                background-color: #000000;
                color: #ffffff;
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
            }}
            .stats-container {{
                margin: 20px;
                padding: 20px;
                background-color: #1a1a1a;
                border-radius: 8px;
                border: 1px solid #333;
            }}
            .stats-title {{
                font-size: 24px;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 15px;
                text-align: center;
            }}
            .stats-item {{
                font-size: 18px;
                margin: 10px 0;
                padding: 10px;
                background-color: #2a2a2a;
                border-radius: 5px;
                border-left: 4px solid #4CAF50;
                color: #ffffff;
            }}
            .growth-positive {{
                color: #4CAF50;
                font-weight: bold;
            }}
            .growth-negative {{
                color: #f44336;
                font-weight: bold;
            }}
        </style>
    </head>
    <body>
        <div class="stats-container">
            <div class="stats-title">粉丝数据统计报告</div>
            <div class="stats-item">
                <strong>统计期间：</strong>{date1.replace('.json', '')} 至 {date2.replace('.json', '')}
            </div>
            <div class="stats-item">
                <strong>有效成员数：</strong>{len(effective_growth_details)} 人（截至结束日期仍在社团）
            </div>
            <div class="stats-item">
                <strong>有效粉丝增长总数：</strong>
                <span class="{'growth-positive' if effective_growth_total >= 0 else 'growth-negative'}">
                    {'+' if effective_growth_total >= 0 else ''}{effective_growth_total:,} 人 ({'+' if effective_growth_wan >= 0 else ''}{effective_growth_wan}万)
                </span>
            </div>
        </div>
        <div id="pie" style="width: 1000px;height:600px;"></div>
        <div id="line" style="width: 1200px;height:800px;"></div>
        <script type="text/javascript">
            // 饼图 - 黑暗模式
            var pieChart = echarts.init(document.getElementById('pie'), 'dark');
            var pieOption = {{
                title: {{
                    text: '粉丝增长量占比（单位：万）',
                    left: 'center',
                    textStyle: {{
                        color: '#ffffff'
                    }}
                }},
                backgroundColor: '#1a1a1a',
                tooltip: {{
                    trigger: 'item',
                    formatter: function(params) {{
                        return params.data.name + '<br/>增长量: ' + params.data.value + '万' +
                            '<br/>占比: ' + params.data.percent + '%';
                    }}
                }},
                legend: {{
                    orient: 'vertical',
                    left: 'left',
                    data: {legend_data_json},
                    textStyle: {{
                        color: '#ffffff'
                    }}
                }},
                series: [{{
                    name: '粉丝数',
                    type: 'pie',
                    radius: '50%',
                    data: {pie_data_json},
                    emphasis: {{
                        itemStyle: {{
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }}
                    }}
                }}]
            }};
            pieChart.setOption(pieOption);
            
            // 折线图 - 黑暗模式
            var lineChart = echarts.init(document.getElementById('line'), 'dark');
            var lineOption = {{
                title: {{
                    text: '成员粉丝增长数量变化趋势（单位：万）',
                    left: 'center',
                    textStyle: {{
                        color: '#ffffff'
                    }}
                }},
                backgroundColor: '#1a1a1a',
                tooltip: {{
                    trigger: 'axis',
                    formatter: function(params) {{
                        var result = params[0].axisValueLabel + '<br/>';
                        params.forEach(function(item) {{
                            if (item.value !== null) {{
                                var sign = item.value >= 0 ? '+' : '';
                                result += item.marker + item.seriesName + ': ' + sign + item.value + '万<br/>';
                            }}
                        }});
                        return result;
                    }}
                }},
                legend: {{
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 30,
                    data: {json.dumps([series['name'] for series in line_series], ensure_ascii=False)},
                    textStyle: {{
                        color: '#ffffff'
                    }}
                }},
                grid: {{
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                }},
                xAxis: {{
                    type: 'category',
                    boundaryGap: false,
                    data: {dates_json},
                    axisLabel: {{
                        color: '#ffffff'
                    }},
                    axisLine: {{
                        lineStyle: {{
                            color: '#ffffff'
                        }}
                    }}
                }},
                yAxis: {{
                    type: 'value',
                    name: '粉丝增长量（万）',
                    nameTextStyle: {{
                        color: '#ffffff'
                    }},
                    axisLabel: {{
                        color: '#ffffff',
                        formatter: function(value) {{
                            return value >= 0 ? '+' + value : value;
                        }}
                    }},
                    axisLine: {{
                        lineStyle: {{
                            color: '#ffffff'
                        }}
                    }},
                    splitLine: {{
                        lineStyle: {{
                            color: '#333333'
                        }}
                    }}
                }},
                series: {line_series_json}
            }};
            lineChart.setOption(lineOption);
        </script>
    </body>
    </html>
    '''
    
    # 写入文件
    with open('pie_chart.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    # 用浏览器打开
    webbrowser.open('pie_chart.html')

if __name__ == "__main__":
    main()
