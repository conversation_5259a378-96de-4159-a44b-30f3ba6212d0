# 应用软件系统开发赛项 - 模块二学习计划

## 📋 竞赛概况

- **竞赛时间**：4小时
- **模块二分值**：55分（占比最高）
- **考核重点**：前端页面开发 + 后端业务开发

## 🎯 模块二技能要求分析

### 核心技术栈
- **前端**：HTML5、CSS3、JavaScript、Vue.js、ElementUI、vue-element-admin
- **后端**：Spring Boot、RESTful API、MySQL数据库
- **架构模式**：
  - 前端：MVVM模式
  - 后端：Domain/POJO、DAO、Service、Controller分层架构
- **部署**：Maven打包、Nginx部署、多端口配置

### 具体任务分析
1. **任务1**：承运申请审核（10分）- 管理前端界面开发
2. **任务2**：销售发货管理（12分）- 全栈开发（前后端+数据库）
3. **任务3**：政策法规（11分）- 用户前端界面
4. **任务4**：参与投标（12分）- 前端页面+数据整合
5. **任务5**：数据可视化（10分）- 图表展示（仪表盘、环状图、柱形图等）

## 📅 两周学习计划

### 第一周：技术基础强化

#### 第1-2天：Spring Boot后端开发
**学习目标**：掌握Spring Boot基础架构和RESTful API开发

**具体内容**：
- [ ] Spring Boot项目结构和配置
- [ ] Controller、Service、DAO分层架构
- [ ] RESTful API设计规范（GET、POST、PUT、DELETE）
- [ ] 数据库连接配置（MySQL）
- [ ] Maven项目管理和打包

**实践任务**：
- [ ] 创建一个简单的CRUD接口
- [ ] 实现分页查询功能
- [ ] 练习Maven打包生成jar文件

**学习资源**：
- Spring Boot官方文档
- 在线教程平台（如慕课网、B站）

#### 第3-4天：数据库设计与操作
**学习目标**：熟练进行数据库设计和SQL操作

**具体内容**：
- [ ] MySQL数据库设计规范
- [ ] 表结构设计（主键、外键、索引）
- [ ] 复杂查询（多表连接、分组统计）
- [ ] 数据库脚本导入导出

**实践任务**：
- [ ] 设计一个完整的业务数据库
- [ ] 练习复杂的统计查询
- [ ] 熟悉数据库可视化工具操作

#### 第5-7天：Vue.js前端开发
**学习目标**：掌握Vue.js和ElementUI组件库

**具体内容**：
- [ ] Vue.js基础语法复习（组件、指令、生命周期）
- [ ] ElementUI组件使用（表格、表单、对话框、分页）
- [ ] vue-element-admin框架结构
- [ ] 前后端数据交互（axios）
- [ ] 前端工程化构建（npm build）

**实践任务**：
- [ ] 实现一个完整的CRUD页面
- [ ] 练习表格分页和搜索功能
- [ ] 熟悉ElementUI常用组件

**重点组件**：
- `el-table`（数据表格）
- `el-form`（表单）
- `el-dialog`（对话框）
- `el-pagination`（分页）
- `el-select`（下拉选择）
- `el-date-picker`（日期选择）

### 第二周：项目实战与综合应用

#### 第8-9天：前后端联调实战
**学习目标**：完成前后端完整功能开发

**具体内容**：
- [ ] 接口联调和数据格式处理
- [ ] 表单验证和错误处理
- [ ] 文件上传下载功能
- [ ] 跨域问题解决

**实践任务**：
- [ ] 模拟任务2：销售发货管理完整开发
- [ ] 实现搜索、分页、增删改查功能
- [ ] 练习对话框和表单操作

#### 第10-11天：数据可视化开发
**学习目标**：掌握图表组件和数据展示

**具体内容**：
- [ ] ECharts图表库使用
- [ ] 仪表盘、环状图、柱形图、曲线图
- [ ] 实时数据更新和动态展示
- [ ] 滚动表格实现

**实践任务**：
- [ ] 实现任务5的所有可视化要求
- [ ] 练习不同类型图表的配置
- [ ] 实现数据的动态更新

**图表类型**：
- 仪表盘（耗电量、耗水量、碳排放量）
- 环状图（能耗占比）
- 滚动表格（库存预警）
- 统计图表（销售数据、碳排放排行）

#### 第12天：系统部署与配置
**学习目标**：掌握项目部署和多端口配置

**具体内容**：
- [ ] Nginx配置和多端口部署
- [ ] 前端项目打包和发布
- [ ] 后端jar包运行
- [ ] 环境配置和端口管理

**实践任务**：
- [ ] 配置三个不同端口的前端项目
  - 管理端：http://IP:8088
  - 用户端：http://IP:8081
  - 可视化：http://IP:8080
- [ ] 练习完整的部署流程
- [ ] 验证各个模块功能

#### 第13-14天：模拟练习与查漏补缺
**学习目标**：综合练习和时间管理

**具体内容**：
- [ ] 完整模拟竞赛流程
- [ ] 时间分配练习（4小时完成所有任务）
- [ ] 常见问题排查和解决
- [ ] 代码规范和注释要求

**实践任务**：
- [ ] 按竞赛要求完成所有5个任务
- [ ] 练习快速定位和解决问题
- [ ] 熟悉文件提交和打包要求

**时间分配建议**：
- 任务1（10分）：45分钟
- 任务2（12分）：60分钟
- 任务3（11分）：50分钟
- 任务4（12分）：60分钟
- 任务5（10分）：45分钟

## 🔍 针对你的问题分析

### 需要重点关注的技术点

1. **JavaScript ES6+语法复习**
   - 箭头函数、解构赋值、Promise、async/await
   - 数组方法（map、filter、reduce）

2. **Vue.js框架学习**（重点）
   - 组件化开发思想
   - 数据绑定和事件处理
   - 组件间通信

3. **Spring Boot框架**
   - 注解使用（@RestController、@Service、@Autowired）
   - 配置文件管理
   - 异常处理

4. **前后端分离开发模式**
   - API接口设计
   - 数据格式统一
   - 错误处理机制

## 💡 学习建议

### 每日学习安排
- **理论学习**：2小时
- **实践练习**：4小时
- **总结复习**：30分钟

### 学习方法
1. **边学边练**：每学一个知识点立即实践
2. **项目驱动**：以竞赛任务为导向学习
3. **问题记录**：建立错误和解决方案笔记
4. **定期回顾**：每3天回顾一次学习内容

### 重要提醒
- 熟悉ElementUI组件库的使用
- 多练习完整的前后端联调
- 掌握常见的调试技巧
- 注意代码规范和注释要求
- 练习在时间压力下的开发效率

## 📚 推荐学习资源

### 在线文档
- [Vue.js官方文档](https://cn.vuejs.org/)
- [Element UI官方文档](https://element.eleme.cn/)
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [ECharts官方文档](https://echarts.apache.org/zh/index.html)

### 视频教程
- B站Vue.js教程
- 慕课网Spring Boot实战
- 尚硅谷前端教程

### 实践平台
- GitHub代码仓库
- 码云Gitee
- 本地开发环境搭建

## ✅ 学习进度跟踪

### 第一周完成情况
- [ ] Day 1-2: Spring Boot基础
- [ ] Day 3-4: 数据库设计
- [ ] Day 5-7: Vue.js前端

### 第二周完成情况
- [ ] Day 8-9: 前后端联调
- [ ] Day 10-11: 数据可视化
- [ ] Day 12: 系统部署
- [ ] Day 13-14: 模拟练习

---

**最后提醒**：这个学习计划时间紧凑，需要你全身心投入。遇到问题及时查阅文档或寻求帮助，保持学习的连续性和实践性。加油！🚀
