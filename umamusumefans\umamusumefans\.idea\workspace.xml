<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="801ae28c-4b3d-4bff-b6ee-271cf8545e24" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/.gitattributes" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradlew" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradlew.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/settings.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/umamusume/fandata/UmamusumefansApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/resources/application.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/umamusume/fandata/UmamusumefansApplicationTests.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="30mXgDNlayQNyDdmdDdpI0KzJIz" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="801ae28c-4b3d-4bff-b6ee-271cf8545e24" name="更改" comment="" />
      <created>1754233834009</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754233834009</updated>
      <workItem from="1754233835471" duration="68000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>