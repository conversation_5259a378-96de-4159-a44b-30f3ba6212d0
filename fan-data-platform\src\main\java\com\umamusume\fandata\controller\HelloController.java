package com.umamusume.fandata.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class HelloController {

    @GetMapping("/hello")
    public String hello() {
        return "赛马娘粉丝数据平台启动成功！";
    }

    @GetMapping("/status")
    public String status() {
        return "系统运行正常";
    }
}
