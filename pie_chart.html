
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>粉丝数据统计</title>
        <script src="echarts.js"></script>
        <style>
            body {
                background-color: #000000;
                color: #ffffff;
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
            }
            .stats-container {
                margin: 20px;
                padding: 20px;
                background-color: #1a1a1a;
                border-radius: 8px;
                border: 1px solid #333;
            }
            .stats-title {
                font-size: 24px;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 15px;
                text-align: center;
            }
            .stats-item {
                font-size: 18px;
                margin: 10px 0;
                padding: 10px;
                background-color: #2a2a2a;
                border-radius: 5px;
                border-left: 4px solid #4CAF50;
                color: #ffffff;
            }
            .growth-positive {
                color: #4CAF50;
                font-weight: bold;
            }
            .growth-negative {
                color: #f44336;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="stats-container">
            <div class="stats-title">粉丝数据统计报告</div>
            <div class="stats-item">
                <strong>统计期间：</strong>20250801 至 20250803
            </div>
            <div class="stats-item">
                <strong>有效成员数：</strong>36 人（截至结束日期仍在社团）
            </div>
            <div class="stats-item">
                <strong>有效粉丝增长总数：</strong>
                <span class="growth-positive">
                    +127,384,113 人 (+12738.41万)
                </span>
            </div>
        </div>
        <div id="pie" style="width: 1000px;height:600px;"></div>
        <div id="line" style="width: 1200px;height:800px;"></div>
        <script type="text/javascript">
            // 饼图 - 黑暗模式
            var pieChart = echarts.init(document.getElementById('pie'), 'dark');
            var pieOption = {
                title: {
                    text: '粉丝增长量占比（单位：万）',
                    left: 'center',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                backgroundColor: '#1a1a1a',
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return params.data.name + '<br/>增长量: ' + params.data.value + '万' +
                            '<br/>占比: ' + params.data.percent + '%';
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ["ムツミ大旋風！ (+2075.44万)", "ネオス (+1261.73万)", "右右 (+915.01万)", "Moregrey (+889.3万)", "那由多 (+782.28万)", "gugugaga (+773.1万)", "来撕 (+688.91万)", "辣条菌． (+665.68万)", "LovesOnlyU (+549.05万)", "阿兔p (+517.6万)", "Svengalis (+448.19万)", "溪烟柳 (+446.5万)", "涯migiwa (+389.2万)", "洛七 (+322.08万)", "美浦波旁的英雄 (+306.37万)", "Asa (+259.1万)", "Yuki (+211.94万)", "凉笙 (+195.83万)", "海老冢智 (+192.25万)", "weather (+168.86万)", "晓美焰 (+165.83万)", "uruosai (+145.03万)", "みき (+124.13万)", "Kin (+78.75万)", "空門蒼 (+75.51万)", "面白是区中之王 (+47.94万)", "syuhsnfh (+42.79万)"],
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                series: [{
                    name: '粉丝数',
                    type: 'pie',
                    radius: '50%',
                    data: [{"name": "ムツミ大旋風！ (+2075.44万)", "value": 2075.44, "percent": 16.3}, {"name": "ネオス (+1261.73万)", "value": 1261.73, "percent": 9.9}, {"name": "右右 (+915.01万)", "value": 915.01, "percent": 7.2}, {"name": "Moregrey (+889.3万)", "value": 889.3, "percent": 7.0}, {"name": "那由多 (+782.28万)", "value": 782.28, "percent": 6.1}, {"name": "gugugaga (+773.1万)", "value": 773.1, "percent": 6.1}, {"name": "来撕 (+688.91万)", "value": 688.91, "percent": 5.4}, {"name": "辣条菌． (+665.68万)", "value": 665.68, "percent": 5.2}, {"name": "LovesOnlyU (+549.05万)", "value": 549.05, "percent": 4.3}, {"name": "阿兔p (+517.6万)", "value": 517.6, "percent": 4.1}, {"name": "Svengalis (+448.19万)", "value": 448.19, "percent": 3.5}, {"name": "溪烟柳 (+446.5万)", "value": 446.5, "percent": 3.5}, {"name": "涯migiwa (+389.2万)", "value": 389.2, "percent": 3.1}, {"name": "洛七 (+322.08万)", "value": 322.08, "percent": 2.5}, {"name": "美浦波旁的英雄 (+306.37万)", "value": 306.37, "percent": 2.4}, {"name": "Asa (+259.1万)", "value": 259.1, "percent": 2.0}, {"name": "Yuki (+211.94万)", "value": 211.94, "percent": 1.7}, {"name": "凉笙 (+195.83万)", "value": 195.83, "percent": 1.5}, {"name": "海老冢智 (+192.25万)", "value": 192.25, "percent": 1.5}, {"name": "weather (+168.86万)", "value": 168.86, "percent": 1.3}, {"name": "晓美焰 (+165.83万)", "value": 165.83, "percent": 1.3}, {"name": "uruosai (+145.03万)", "value": 145.03, "percent": 1.1}, {"name": "みき (+124.13万)", "value": 124.13, "percent": 1.0}, {"name": "Kin (+78.75万)", "value": 78.75, "percent": 0.6}, {"name": "空門蒼 (+75.51万)", "value": 75.51, "percent": 0.6}, {"name": "面白是区中之王 (+47.94万)", "value": 47.94, "percent": 0.4}, {"name": "syuhsnfh (+42.79万)", "value": 42.79, "percent": 0.3}],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            pieChart.setOption(pieOption);
            
            // 折线图 - 黑暗模式
            var lineChart = echarts.init(document.getElementById('line'), 'dark');
            var lineOption = {
                title: {
                    text: '成员粉丝增长数量变化趋势（单位：万）',
                    left: 'center',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                backgroundColor: '#1a1a1a',
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        var result = params[0].axisValueLabel + '<br/>';
                        params.forEach(function(item) {
                            if (item.value !== null) {
                                var sign = item.value >= 0 ? '+' : '';
                                result += item.marker + item.seriesName + ': ' + sign + item.value + '万<br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 30,
                    data: ["涯migiwa", "Svengalis", "ネオス", "右右", "海老冢智", "阿兔p", "来撕", "凉笙", "Moregrey", "Yuki", "溪烟柳", "美浦波旁的英雄", "Asa", "ムツミ大旋風！", "uruosai", "辣条菌．", "gugugaga", "星火夜", "syuhsnfh", "LovesOnlyU", "九月清風", "那由多", "面白是区中之王", "weather", "空門蒼", "みき", "tomotaji", "晓美焰", "Kin", "洛七", "みっちん", "菌物", "小任性", "我叫ツナミ", "八奈見杏菜", "爱UA的小八"],
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ["20250701", "20250704", "20250706", "20250707", "20250708", "20250710", "20250712", "20250713", "20250714", "20250721", "20250722", "20250723", "20250725", "20250726", "20250727", "20250728", "20250730", "20250731", "20250801", "20250802", "20250803"],
                    axisLabel: {
                        color: '#ffffff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ffffff'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '粉丝增长量（万）',
                    nameTextStyle: {
                        color: '#ffffff'
                    },
                    axisLabel: {
                        color: '#ffffff',
                        formatter: function(value) {
                            return value >= 0 ? '+' + value : value;
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ffffff'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#333333'
                        }
                    }
                },
                series: [{"name": "涯migiwa", "type": "line", "data": [0.0, 736.54, 1427.89, 1743.87, 1910.25, 2171.66, 2401.66, 2459.7, 2484.4, 2853.83, 2933.1, 3087.49, 3096.91, 3110.37, 3124.06, 3262.71, 3341.56, 3351.21, 3436.21, 3559.3, 3825.41], "connectNulls": false}, {"name": "Svengalis", "type": "line", "data": [0.0, 391.97, 400.78, 849.28, 1263.46, 1818.32, 2135.97, 2203.27, 2223.48, 3321.22, 3567.67, 3758.35, 3845.33, 4405.49, 4888.68, 5072.74, 5370.97, 5592.38, 5598.34, 5747.29, 6046.53], "connectNulls": false}, {"name": "ネオス", "type": "line", "data": [0.0, 442.67, 694.52, 1206.83, 1257.42, 1761.28, 2315.65, 2476.16, 2642.63, 3788.5, 4000.87, 4954.55, 5337.32, 5337.32, 5338.72, 5430.43, 5579.07, 5740.47, 5938.01, 6397.99, 7199.74], "connectNulls": false}, {"name": "右右", "type": "line", "data": [0.0, 318.5, 613.16, 804.08, 804.08, 928.69, 1085.97, 1247.64, 1801.07, 4496.99, 4552.92, 5396.93, 6207.01, 6514.01, 6904.98, 7779.67, 8084.97, 8340.45, 8467.48, 8622.29, 9382.5], "connectNulls": false}, {"name": "海老冢智", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 192.25], "connectNulls": false}, {"name": "阿兔p", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 244.05, 517.6], "connectNulls": false}, {"name": "来撕", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 93.06, 688.91], "connectNulls": false}, {"name": "凉笙", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 82.51, 195.83], "connectNulls": false}, {"name": "Moregrey", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 889.3], "connectNulls": false}, {"name": "Yuki", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 68.24, 211.94], "connectNulls": false}, {"name": "溪烟柳", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 193.52, 446.5], "connectNulls": false}, {"name": "美浦波旁的英雄", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 90.23, 306.37], "connectNulls": false}, {"name": "Asa", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 1.53, 259.1], "connectNulls": false}, {"name": "ムツミ大旋風！", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 445.85, 2075.44], "connectNulls": false}, {"name": "uruosai", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 70.1, 145.03], "connectNulls": false}, {"name": "辣条菌．", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 42.27, 665.68], "connectNulls": false}, {"name": "gugugaga", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 161.65, 773.1], "connectNulls": false}, {"name": "星火夜", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 0.0], "connectNulls": false}, {"name": "syuhsnfh", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 6.66, 42.79], "connectNulls": false}, {"name": "LovesOnlyU", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 549.05], "connectNulls": false}, {"name": "九月清風", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 0.0], "connectNulls": false}, {"name": "那由多", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 782.28], "connectNulls": false}, {"name": "面白是区中之王", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 47.94, 47.94], "connectNulls": false}, {"name": "weather", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 39.73, 168.86], "connectNulls": false}, {"name": "空門蒼", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 36.87, 75.51], "connectNulls": false}, {"name": "みき", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 26.21, 124.13], "connectNulls": false}, {"name": "tomotaji", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 0.0], "connectNulls": false}, {"name": "晓美焰", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 39.98, 165.83], "connectNulls": false}, {"name": "Kin", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0, 78.75], "connectNulls": false}, {"name": "洛七", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 248.97, 322.08], "connectNulls": false}, {"name": "みっちん", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0], "connectNulls": false}, {"name": "菌物", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0], "connectNulls": false}, {"name": "小任性", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0], "connectNulls": false}, {"name": "我叫ツナミ", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0], "connectNulls": false}, {"name": "八奈見杏菜", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0], "connectNulls": false}, {"name": "爱UA的小八", "type": "line", "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0.0, 0.0], "connectNulls": false}]
            };
            lineChart.setOption(lineOption);
        </script>
    </body>
    </html>
    